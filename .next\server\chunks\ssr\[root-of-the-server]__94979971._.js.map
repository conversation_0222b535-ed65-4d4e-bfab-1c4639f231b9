{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/cursor%20project/vercel/src/app/page.tsx"], "sourcesContent": ["export default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                我的测试网站\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <a href=\"#about\" className=\"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\">\n                关于\n              </a>\n              <a href=\"#contact\" className=\"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\">\n                联系\n              </a>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* 主要内容 */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* 英雄区域 */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6\">\n            欢迎来到我的\n            <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600\">\n              测试网站\n            </span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto\">\n            这是一个使用 Next.js 和 Tailwind CSS 构建的现代化网站，部署在 Vercel 上。\n            展示了响应式设计和现代 Web 开发技术。\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button className=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors shadow-lg\">\n              开始探索\n            </button>\n            <button className=\"border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 font-semibold py-3 px-8 rounded-lg transition-colors\">\n              了解更多\n            </button>\n          </div>\n        </div>\n\n        {/* 功能卡片 */}\n        <div className=\"grid md:grid-cols-3 gap-8 mb-16\">\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow\">\n            <div className=\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4\">\n              <svg className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">快速部署</h3>\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              使用 Vercel 平台实现快速、可靠的网站部署，支持自动化 CI/CD 流程。\n            </p>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow\">\n            <div className=\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-4\">\n              <svg className=\"w-6 h-6 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">现代设计</h3>\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              采用现代化的设计理念，支持深色模式，提供优秀的用户体验。\n            </p>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow\">\n            <div className=\"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-4\">\n              <svg className=\"w-6 h-6 text-purple-600 dark:text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">技术先进</h3>\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              基于 Next.js 14、React 18 和 TypeScript，使用最新的 Web 技术栈。\n            </p>\n          </div>\n        </div>\n\n        {/* 统计信息 */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8\">\n          <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white text-center mb-8\">项目统计</h3>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2\">100%</div>\n              <div className=\"text-gray-600 dark:text-gray-300\">响应式设计</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-green-600 dark:text-green-400 mb-2\">A+</div>\n              <div className=\"text-gray-600 dark:text-gray-300\">性能评分</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2\">24/7</div>\n              <div className=\"text-gray-600 dark:text-gray-300\">在线时间</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-orange-600 dark:text-orange-400 mb-2\">∞</div>\n              <div className=\"text-gray-600 dark:text-gray-300\">扩展性</div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* 页脚 */}\n      <footer className=\"bg-gray-900 text-white py-12 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h3 className=\"text-xl font-semibold mb-4\">感谢访问我的测试网站</h3>\n            <p className=\"text-gray-400 mb-6\">\n              这个网站展示了现代 Web 开发的最佳实践，包括响应式设计、性能优化和用户体验。\n            </p>\n            <div className=\"flex justify-center space-x-6\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                GitHub\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                Twitter\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                LinkedIn\n              </a>\n            </div>\n            <div className=\"mt-8 pt-8 border-t border-gray-800 text-gray-400 text-sm\">\n              © 2024 测试网站. 使用 Next.js 和 Vercel 构建.\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkD;;;;;;;;;;;0CAIlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAkG;;;;;;kDAG7H,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAkG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvI,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoE;kDAEhF,8OAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;0CAI/F,8OAAC;gCAAE,WAAU;0CAAkE;;;;;;0CAI/E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA0G;;;;;;kDAG5H,8OAAC;wCAAO,WAAU;kDAA4K;;;;;;;;;;;;;;;;;;kCAOlM,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAA2C,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAClG,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAKlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAA6C,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACpG,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAKlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAA+C,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtG,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;kCAOpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAClF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA2D;;;;;;0DAC1E,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;;;;;;;kDAEpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6D;;;;;;0DAC5E,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;;;;;;;kDAEpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+D;;;;;;0DAC9E,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;;;;;;;kDAEpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+D;;;;;;0DAC9E,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAmD;;;;;;kDAGzE,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAmD;;;;;;kDAGzE,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAmD;;;;;;;;;;;;0CAI3E,8OAAC;gCAAI,WAAU;0CAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtF", "debugId": null}}]}